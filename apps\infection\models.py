from django.db import models
from django.utils import timezone
from config.models import BaseModel
from django.utils.translation import gettext_lazy as _


class Device(BaseModel):
    """设备模型"""
    exercise = models.ForeignKey(to="exercise.Exercise", verbose_name="演练项目", on_delete=models.CASCADE,
                                 related_name="devices", null=True, blank=True)
    hostname = models.CharField('主机名', max_length=255, null=True, blank=True)
    device_id = models.CharField('设备ID', max_length=36, null=True, blank=True)
    first_seen = models.DateTimeField('首次发现时间', default=timezone.now)
    mac_address = models.CharField(_("MAC地址"), max_length=17, blank=True, null=True)
    last_seen = models.DateTimeField('最后活动时间', auto_now=True)
    infection_count = models.IntegerField('感染次数', default=0)

    class Meta:
        db_table = "ls_device"
        verbose_name = '设备信息'
        verbose_name_plural = verbose_name
        ordering = ['-last_seen']

    def __str__(self):
        return self.device_id


class InfectionRecord(BaseModel):
    """感染记录模型"""
    STATUS_CHOICES = (
        ('IN', '已感染'),
        ('EN', '已加密'),
        ('OF', '已下线'),
        ('RC', '已恢复'),
    )
    
    # 添加状态字段
    status = models.CharField(
        '状态',
        max_length=2,
        choices=STATUS_CHOICES,
        default='IN'
    )
    
    # 设备关联
    device = models.ForeignKey(
        Device,
        on_delete=models.CASCADE,
        related_name='infection_records',
        verbose_name='关联设备',
        null=True,  # 临时允许为空
        blank=True  # 临时允许为空
    )

    exercise = models.ForeignKey(to="exercise.Exercise", verbose_name="演练项目", on_delete=models.CASCADE,
                                 related_name="infection_records", null=True, blank=True)
    
    # 客户端信息字段
    hostname = models.CharField('主机名', max_length=255)
    username = models.CharField('用户名', max_length=255)
    exec_path = models.CharField('执行路径', max_length=1024, null=True, blank=True)
    system_time = models.DateTimeField('系统时间', default=timezone.now, null=True, blank=True)
    ip_address = models.GenericIPAddressField('IP地址', default='0.0.0.0', null=True, blank=True)
    location = models.CharField('位置信息', max_length=255, null=True, blank=True)
    system_version = models.CharField('系统版本', max_length=255, null=True, blank=True)

    class Meta:
        verbose_name = '感染记录'
        verbose_name_plural = verbose_name
        ordering = ['-system_time']

    def __str__(self):
        return f"{self.device.device_id if self.device else 'Unknown'} - {self.hostname}"


class DeviceCommand(BaseModel):
    """设备命令记录模型"""
    COMMAND_CHOICES = [
        ('all', '获取在线设备'),
        ('sc', '更改壁纸'),
        ('rc', '恢复壁纸'),
        ('enc', '开始加密'),
        ('dec', '开始解密'),
        ('exit', '销毁病毒'),
        ('kill', '关闭杀软'),
    ]
    
    device = models.ForeignKey(
        Device,
        on_delete=models.CASCADE,
        related_name='commands',
        verbose_name='设备',
        null=True,
        blank=True
    )
    exercise = models.ForeignKey(to="exercise.Exercise", verbose_name="演练项目", on_delete=models.CASCADE,
                                 related_name="commands", null=True, blank=True)
    command = models.CharField('命令类型', max_length=10, choices=COMMAND_CHOICES)
    args = models.JSONField('命令参数', default=dict)
    response = models.JSONField('响应结果', default=dict)
    status = models.BooleanField('执行状态', default=False)

    class Meta:
        verbose_name = '设备命令'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']

    def __str__(self):
        return f'{self.device.device_id if self.device else "All"} - {self.command}'
