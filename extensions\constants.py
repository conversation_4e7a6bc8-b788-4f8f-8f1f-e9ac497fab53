# 上传文件所在路径
import os

UPLOAD_FILE_PATH_DIC = {
    'email_template': 'email_template',  # 邮件模板
    'virus_family_tools': 'virus_family_tools',  # 勒索家族工具附件
    'negotiation_record': 'negotiation_record',  # 聊天记录
    'virus_family_logo': 'virus_family_logo',  # 病毒家族logo
    'encryptor': 'encryptor',  # 加密器
    'wallpaper': 'wallpaper',  # 壁纸
    'avatar': 'avatar',  # 头像
    'company_logo': 'company_logo',  # 公司logo
}

# 资产类型
Asset_TYPE_EP = 'EP'  # 终端
Asset_TYPE_SV = 'SV'  # 服务器
Asset_TYPE_NW = 'NW'  # 网络设备
Asset_TYPE_EM = 'EM'  # 电子邮件

# 资产操作系统内容与key对应关系
ASSET_OS_TYPE = {
    "Windows7": "WIN7",
    "Windows10": "WIN10",
    "Windows10网信版": "WIN10_W",
    "Windows11": "WIN11",
    "CentOS7": "CENTOS_7",
    "WindowsServer": "WIN_SERVER",
    "kali_linux": "KALI_LINUX",
    "ios": "IOS",
    "MacOS": "MACOS",
    "其他": "OTHER",
}

ASSET_OS_TO_TYPE = {
    "WIN7": "Windows7",
    "WIN10": "Windows10",
    "WIN10_W": "Windows10网信版",
    "WIN11": "Windows11",
    "CENTOS_7": "CentOS7",
    "WIN_SERVER": "WindowsServer",
    "KALI_LINUX": "kali_linux",
    "IOS": "ios",
    "MACOS": "MacOS",
    "OTHER": "其他",
}

# 钓鱼表单地址
PHISHING_LINK = "https://admin.fanglesuo.cn/api/v1/form"
# 邮件发送模板隐藏链接构造
EMAIL_IMAGE_URL = f'<img src="{os.environ.get("RESERVE_EMAIL")}?email=%s&exercise_id=%s" width="1" height="1">'


# 访问钓鱼邮件状态
FORM_STATUS_CLICK = 'CLICK'  # 点击链接
FORM_STATUS_SUBMIT = 'SUBMIT'  # 提交表单

# 病毒地址
ENCRYPTOR_FILE_URL = "https://ransomware-emergency.oss-cn-qingdao.aliyuncs.com/virus_family_samples/20250523180144_Client_V2.1.exe"
