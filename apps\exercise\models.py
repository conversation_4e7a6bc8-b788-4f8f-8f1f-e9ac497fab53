from django.db import models
from apps.users.models import User
from apps.virus.models import Virus
from apps.assets.models import Asset, AssetGroup
from config.models import BaseModel


class Exercise(BaseModel):
    """演练项目模型"""

    class Status(models.TextChoices):
        PENDING = "PE", "待开始"  # 演练已创建但未开始
        RUNNING = "RU", "进行中"  # 演练正在执行
        PAUSED = "PA", "已暂停"  # 演练暂时停止
        FINISHED = "FI", "已完成"  # 演练已结束
        TERMINATED = "TE", "已终止"  # 演练被手动终止

    name = models.CharField(
        "演练名称", max_length=100, help_text="演练项目的名称，用于标识不同的演练"
    )
    virus = models.ForeignKey(
        Virus,
        on_delete=models.PROTECT,  # 防止删除正在使用的病毒样本
        related_name="exercises",
        verbose_name="使用病毒",
        help_text="选择本次演练使用的病毒样本",
    )
    # 邮件任务
    email_task = models.ForeignKey(
        to="phishing.EmailTakModel",
        on_delete=models.PROTECT,
        related_name="exercises",
        verbose_name="钓鱼任务",
        help_text="选择本次演练使用的钓鱼邮件任务",
        null=True, blank=True
    )
    # 谈判配置
    negotiation = models.ForeignKey(
        to="virus.NegotiationModel",
        on_delete=models.PROTECT,
        related_name="exercises",
        verbose_name="谈判配置",
        help_text="选择本次演练使用的谈判配置任务",
        null=True, blank=True
    )
    target_groups = models.ManyToManyField(
        AssetGroup,
        related_name="groups_exercises",
        verbose_name="目标资产组",
        blank=True,
        help_text="选择本次演练的目标资产组",
    )
    target_asset = models.ManyToManyField(
        AssetGroup,
        related_name="assets_exercises",
        verbose_name="邮箱信息组",
        blank=True,
        help_text="选择本次邮箱信息组",
    )
    start_time = models.DateTimeField("开始时间", help_text="演练计划开始的时间", null=True, blank=True)
    end_time = models.DateTimeField("结束时间", help_text="演练计划结束的时间", null=True, blank=True)
    status = models.CharField(
        "状态",
        max_length=2,
        choices=Status.choices,
        default=Status.PENDING,
        help_text="当前演练的执行状态",
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="created_exercises",
        verbose_name="创建者",
    )
    task_id = models.CharField(
        "任务ID",
        max_length=50,
        null=True,
        blank=True,
        help_text="存储定时任务的Django Q2任务ID"
    )

    class Meta:
        db_table = "ls_exercise"
        verbose_name = "演练任务"
        verbose_name_plural = verbose_name
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.name} ({self.get_status_display()})"
