# 基础镜像，使用官方的python镜像
FROM python:3.10-slim

# 创建非特权用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 设置工作目录
WORKDIR /app

# 创建必要的目录并设置权限
RUN mkdir -p /app/media /app/static /app/logs \
    && chmod -R 755 /app/media /app/static /app/logs \
    && chown -R appuser:appuser /app/media /app/static /app/logs

# 设置环境变量
ENV DJANGO_ENV=product \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    STATIC_ROOT=/app/static \
    MEDIA_ROOT=/app/media

# 设置Debian国内镜像源
RUN echo "deb https://mirrors.ustc.edu.cn/debian/ bullseye main contrib non-free" > /etc/apt/sources.list && \
    echo "deb https://mirrors.ustc.edu.cn/debian/ bullseye-updates main contrib non-free" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.ustc.edu.cn/debian/ bullseye-backports main contrib non-free" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.ustc.edu.cn/debian-security bullseye-security main contrib non-free" >> /etc/apt/sources.list

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    supervisor \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 复制项目的依赖文件
COPY requirements.txt .

# 设置 pip 阿里云镜像
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/

# 更新pip并安装依赖
RUN pip install --upgrade pip \
    && pip install --no-cache-dir -r requirements.txt

# 复制整个项目到工作目录
COPY . .

# 复制supervisor配置文件
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf

# 复制启动脚本
COPY entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# 生成静态文件
RUN python manage.py collectstatic --noinput \
    && chmod -R 755 /app/static \
    && chown -R appuser:appuser /app/static

# 设置权限
RUN chown -R appuser:appuser /app \
    && chmod +x /app/entrypoint.sh

# 切换到非特权用户
USER appuser

# 暴露端口
EXPOSE 8001

# 使用启动脚本
CMD ["/app/entrypoint.sh"]
