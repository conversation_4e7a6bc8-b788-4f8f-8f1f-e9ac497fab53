from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
from django.core.exceptions import ValidationError
import mimetypes
from .models import VirusFamily
from .serializers import (
    VirusFamilySerializer,
    FileUploadSerializer
)
from drf_spectacular.utils import extend_schema, extend_schema_view


@extend_schema_view(
    list=extend_schema(
        summary="获取病毒家族列表",
        description="获取所有病毒家族信息，包括基本信息、Logo、加密算法等",
        tags=["病毒家族"]
    ),
    create=extend_schema(
        summary="创建病毒家族",
        description="创建新的病毒家族，包括上传Logo、设置加密算法、勒索方式等信息",
        tags=["病毒家族"]
    ),
    retrieve=extend_schema(
        summary="获取病毒家族详情",
        description="获取指定病毒家族的详细信息，包括关联的勒索信、钱包地址、工具等",
        tags=["病毒家族"]
    ),
    update=extend_schema(
        summary="更新病毒家族",
        description="更新指定病毒家族的信息，包括Logo、加密算法、关联信息等",
        tags=["病毒家族"]
    ),
    partial_update=extend_schema(
        summary="部分更新病毒家族",
        description="部分更新指定病毒家族的信息",
        tags=["病毒家族"]
    ),
    destroy=extend_schema(
        summary="删除病毒家族",
        description="删除指定的病毒家族及其关联信息",
        tags=["病毒家族"]
    )
)
class VirusFamilyViewSet(viewsets.ModelViewSet):
    """病毒家族视图集"""
    queryset = VirusFamily.objects.all()
    serializer_class = VirusFamilySerializer
    permission_classes = [permissions.IsAuthenticated]

    # 文件上传配置
    ALLOWED_IMAGE_TYPES = {'image/jpeg', 'image/png', 'image/gif', 'image/webp'}
    ALLOWED_DOCUMENT_TYPES = {
        'application/pdf', 'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain'
    }
    ALLOWED_BINARY_TYPES = {
        'application/x-msdownload',  # .exe
        'application/x-msdos-program',  # .exe
        'application/x-dosexec',  # .exe
        'application/x-executable',  # Linux executable
        'application/octet-stream',  # Binary file
        'application/x-elf',  # ELF file
        'application/x-sharedlib',  # Shared library
        'application/x-object',  # Object file
        'application/x-pie-executable',  # PIE executable
    }
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

    def get_serializer_context(self):
        """添加request到序列化器上下文"""
        context = super().get_serializer_context()
        context.update({'request': self.request})
        return context

    def get_upload_path(self, file_type):
        """根据文件类型获取上传路径
        
        Args:
            file_type: 文件MIME类型
            
        Returns:
            str: 上传路径
        """
        if file_type in self.ALLOWED_IMAGE_TYPES:
            return 'virus_family_logo'
        elif file_type in self.ALLOWED_DOCUMENT_TYPES:
            return 'negotiation_record'
        elif file_type in self.ALLOWED_BINARY_TYPES:
            return 'virus_family_samples'
        else:
            return 'virus_family_tools'

    def validate_file(self, file):
        """验证文件
        
        Args:
            file: 上传的文件对象
            
        Returns:
            tuple: (文件类型, 上传路径)
            
        Raises:
            ValidationError: 当文件验证失败时抛出
        """
        # 检查文件大小
        if file.size > self.MAX_FILE_SIZE:
            raise ValidationError(f'文件大小不能超过{self.MAX_FILE_SIZE/1024/1024}MB')

        # 获取文件类型
        content_type = mimetypes.guess_type(file.name)[0]
        if not content_type:
            # 对于二进制文件，可能无法识别MIME类型，使用通用二进制类型
            content_type = 'application/octet-stream'

        # 验证文件类型
        allowed_types = self.ALLOWED_IMAGE_TYPES | self.ALLOWED_DOCUMENT_TYPES | self.ALLOWED_BINARY_TYPES
        if content_type not in allowed_types:
            raise ValidationError('不支持的文件类型')

        # 获取上传路径
        upload_path = self.get_upload_path(content_type)
        
        return content_type, upload_path

    def generate_filename(self, original_filename):
        """生成文件名
        
        Args:
            original_filename: 原始文件名
            
        Returns:
            str: 生成的文件名
        """
        timestamp = timezone.now().strftime('%Y%m%d%H%M%S')
        # 获取文件扩展名
        ext = original_filename.split('.')[-1] if '.' in original_filename else ''
        # 生成文件名
        return f"{timestamp}_{original_filename}" if ext else f"{timestamp}_{original_filename}.{ext}"

    @extend_schema(
        summary="上传文件",
        description="上传病毒家族相关文件（Logo、文档等）。支持的图片格式：JPEG、PNG、GIF、WEBP；支持的文档格式：PDF、Word、TXT。文件大小限制：50MB",
        tags=["病毒家族"],
        request=FileUploadSerializer,
        responses={
            201: {
                'type': 'object',
                'properties': {
                    'file_path': {'type': 'string', 'description': '文件存储路径'},
                    'file_url': {'type': 'string', 'description': '文件访问URL'}
                }
            },
            400: {
                'type': 'object',
                'properties': {
                    'error': {'type': 'string', 'description': '错误信息'}
                }
            }
        }
    )
    @action(detail=False, methods=['post'])
    def upload_file(self, request):
        """文件上传处理
        
        根据文件类型自动判断上传路径，使用Django默认存储（阿里云OSS）处理文件上传
        
        Returns:
            Response: 包含文件路径和URL的响应
        """
        serializer = FileUploadSerializer(data=request.data)
        if serializer.is_valid():
            file = serializer.validated_data['file']
            
            try:
                # 验证文件并获取上传路径
                _, upload_path = self.validate_file(file)
                
                # 生成文件名和路径
                filename = self.generate_filename(file.name)
                file_path = f"{upload_path}/{filename}"
                
                # 保存文件到OSS
                saved_path = default_storage.save(file_path, ContentFile(file.read()))
                file_url = default_storage.url(saved_path)
                
                return Response({
                    'file_path': saved_path,
                    'file_url': file_url
                }, status=status.HTTP_201_CREATED)
                
            except ValidationError as e:
                return Response({
                    'error': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                return Response({
                    'error': f'文件上传失败: {str(e)}'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
